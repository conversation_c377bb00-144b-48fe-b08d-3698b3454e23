package com.mlc.workflow.core.editor.detail.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.AUTHENTICATION (22)
 */
@Getter
@Setter
public class AuthenticationNode extends BaseNode {

    public AuthenticationNode() {
        this.setFlowNodeType(NodeTypeEnum.AUTHENTICATION.getValue());
        this.setName("API 连接与认证任务");
    }
}
