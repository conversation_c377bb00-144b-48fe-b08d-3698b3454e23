package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 公式任务节点
 * 对应NodeTypeEnum.FORMULA (9)
 */
@Getter
@Setter
@DataBean
public class FormulaNode extends BaseNode {

    public FormulaNode() {
        this.setTypeId(NodeTypeEnum.FORMULA.getValue());
        this.setName("公式任务");
    }
}
