package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取单条数据任务节点
 * 对应NodeTypeEnum.SEARCH (7)
 */
@Getter
@Setter
@DataBean
public class SearchNode extends BaseNode {

    public SearchNode() {
        this.setTypeId(NodeTypeEnum.SEARCH.getValue());
        this.setName("获取单条数据任务");
    }
}
