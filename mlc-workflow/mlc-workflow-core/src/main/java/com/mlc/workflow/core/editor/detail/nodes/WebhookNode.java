package com.mlc.workflow.core.editor.detail.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 发送 API 请求任务节点
 * 对应NodeTypeEnum.WEBHOOK (8)
 */
@Getter
@Setter
public class WebhookNode extends BaseNode {

    public WebhookNode() {
        this.setFlowNodeType(NodeTypeEnum.WEBHOOK.getValue());
        this.setName("发送 API 请求任务");
    }
}
