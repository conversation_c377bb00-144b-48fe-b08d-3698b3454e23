package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 短信任务节点
 * 对应NodeTypeEnum.MESSAGE (10)
 */
@Getter
@Setter
@DataBean
public class MessageNode extends BaseNode {

    public MessageNode() {
        this.setTypeId(NodeTypeEnum.MESSAGE.getValue());
        this.setName("短信任务");
    }
}
