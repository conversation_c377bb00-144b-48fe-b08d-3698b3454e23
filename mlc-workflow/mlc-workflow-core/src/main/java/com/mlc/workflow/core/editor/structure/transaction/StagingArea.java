package com.mlc.workflow.core.editor.structure.transaction;

import com.mlc.base.common.utils.JacksonDeepCopyUtil;
import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 变更暂存区
 * 管理NodeBatchExecutor中的所有变更，提供工作副本和变更跟踪功能
 */
@Slf4j
public class StagingArea {
    
    /**
     * 原始ProcessNode（只读）
     */
    private final ProcessNode originalProcessNode;
    
    /**
     * 工作副本ProcessNode（可修改）
     */
    @Getter
    private final ProcessNode workingCopy;
    
    /**
     * 变更跟踪集合
     */
    private final Set<String> createdNodeIds = new HashSet<>();
    private final Set<String> modifiedNodeIds = new HashSet<>();
    private final Set<String> deletedNodeIds = new HashSet<>();
    

    /**
     * 构造函数
     * @param originalProcessNode 原始ProcessNode
     */
    public StagingArea(ProcessNode originalProcessNode) {
        this.originalProcessNode = originalProcessNode;
        this.workingCopy = JacksonDeepCopyUtil.deepCopy(originalProcessNode);
        log.debug("创建暂存区，原始节点数量: {}", 
                 originalProcessNode.getFlowNodeMap() != null ? originalProcessNode.getFlowNodeMap().size() : 0);
    }
    
    /**
     * 获取工作副本中的节点
     * @param nodeId 节点ID
     * @return 节点实例
     */
    public BaseNode getNode(String nodeId) {
        if (workingCopy.getFlowNodeMap() == null) {
            return null;
        }
        return workingCopy.getFlowNodeMap().get(nodeId);
    }
    
    /**
     * 在工作副本中创建新节点
     * @param node 新节点
     */
    public void createNode(BaseNode node) {
        if (node == null || node.getId() == null) {
            throw new IllegalArgumentException("节点或节点ID不能为空");
        }
        
        String nodeId = node.getId();
        
        // 检查节点是否已存在
        if (workingCopy.getFlowNodeMap().containsKey(nodeId)) {
            throw new IllegalStateException("节点已存在: " + nodeId);
        }
        
        // 添加到工作副本
        workingCopy.getFlowNodeMap().put(nodeId, node);
        
        // 记录变更
        createdNodeIds.add(nodeId);
        
        log.debug("在暂存区创建节点: {}", nodeId);
    }
    
    /**
     * 在工作副本中更新节点
     * @param nodeId 节点ID
     * @param updatedNode 更新后的节点
     */
    public void updateNode(String nodeId, BaseNode updatedNode) {
        if (nodeId == null || updatedNode == null) {
            throw new IllegalArgumentException("节点ID或更新节点不能为空");
        }
        
        // 检查节点是否存在
        if (!workingCopy.getFlowNodeMap().containsKey(nodeId)) {
            throw new IllegalStateException("节点不存在: " + nodeId);
        }
        
        // 更新工作副本中的节点
        workingCopy.getFlowNodeMap().put(nodeId, updatedNode);
        
        // 记录变更（如果不是新创建的节点）
        if (!createdNodeIds.contains(nodeId)) {
            modifiedNodeIds.add(nodeId);
        }
        
        log.debug("在暂存区更新节点: {}", nodeId);
    }
    
    /**
     * 在工作副本中删除节点
     * @param nodeId 节点ID
     */
    public void deleteNode(String nodeId) {
        if (nodeId == null) {
            throw new IllegalArgumentException("节点ID不能为空");
        }
        
        BaseNode nodeToDelete = workingCopy.getFlowNodeMap().get(nodeId);
        if (nodeToDelete == null) {
            throw new IllegalStateException("节点不存在: " + nodeId);
        }

        // 从工作副本中删除
        workingCopy.getFlowNodeMap().remove(nodeId);
        
        // 记录变更
        if (createdNodeIds.contains(nodeId)) {
            // 如果是新创建的节点，直接从创建列表中移除
            createdNodeIds.remove(nodeId);
        } else {
            // 如果是原有节点，记录为删除
            deletedNodeIds.add(nodeId);
            modifiedNodeIds.remove(nodeId); // 从修改列表中移除
        }
        
        log.debug("在暂存区删除节点: {}", nodeId);
    }
    
    /**
     * 检查节点是否存在于工作副本中
     * @param nodeId 节点ID
     * @return 是否存在
     */
    public boolean containsNode(String nodeId) {
        return workingCopy.getFlowNodeMap() != null && 
               workingCopy.getFlowNodeMap().containsKey(nodeId);
    }
    
    /**
     * 获取所有节点ID
     * @return 节点ID集合
     */
    public Set<String> getAllNodeIds() {
        if (workingCopy.getFlowNodeMap() == null) {
            return Collections.emptySet();
        }
        return new HashSet<>(workingCopy.getFlowNodeMap().keySet());
    }
    
    /**
     * 检查是否有变更
     * @return 是否有变更
     */
    public boolean hasChanges() {
        return !createdNodeIds.isEmpty() || 
               !modifiedNodeIds.isEmpty() || 
               !deletedNodeIds.isEmpty();
    }
    
    /**
     * 获取变更统计信息
     * @return 变更统计
     */
    public ChangeStatistics getChangeStatistics() {
        return new ChangeStatistics(createdNodeIds.size(), modifiedNodeIds.size(), deletedNodeIds.size());
    }
    

    /**
     * 应用所有变更到原始ProcessNode
     * 注意：这个方法会修改原始ProcessNode，只应该在commit时调用
     */
    public void applyChangesToOriginal() {
        log.debug("开始应用变更到原始ProcessNode");
        
        // 应用删除操作
        for (String nodeId : deletedNodeIds) {
            originalProcessNode.getFlowNodeMap().remove(nodeId);
            log.debug("应用删除: {}", nodeId);
        }
        
        // 应用创建和更新操作
        for (String nodeId : createdNodeIds) {
            BaseNode node = workingCopy.getFlowNodeMap().get(nodeId);
            if (node != null) {
                originalProcessNode.getFlowNodeMap().put(nodeId, node);
                log.debug("应用创建: {}", nodeId);
            }
        }
        
        for (String nodeId : modifiedNodeIds) {
            BaseNode node = workingCopy.getFlowNodeMap().get(nodeId);
            if (node != null) {
                originalProcessNode.getFlowNodeMap().put(nodeId, node);
                log.debug("应用更新: {}", nodeId);
            }
        }
        
        log.debug("变更应用完成，最终节点数量: {}", originalProcessNode.getFlowNodeMap().size());
    }
    
    /**
     * 清理暂存区
     */
    public void clear() {
        createdNodeIds.clear();
        modifiedNodeIds.clear();
        deletedNodeIds.clear();
        log.debug("暂存区已清理");
    }

    /**
     * 变更统计信息
     */
    public record ChangeStatistics(int createdCount, int modifiedCount, int deletedCount) {

        public int getTotalChanges() {
            return createdCount + modifiedCount + deletedCount;
        }

        @Override
        public String toString() {
            return String.format("变更统计[创建:%d, 修改:%d, 删除:%d, 总计:%d]",
                                 createdCount, modifiedCount, deletedCount, getTotalChanges());
        }
    }
}
