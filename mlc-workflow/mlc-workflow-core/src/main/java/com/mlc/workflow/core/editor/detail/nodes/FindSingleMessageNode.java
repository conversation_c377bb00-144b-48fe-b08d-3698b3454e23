package com.mlc.workflow.core.editor.detail.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取单条信息任务节点
 * 对应NodeTypeEnum.FIND_SINGLE_MESSAGE (1000)
 */
@Getter
@Setter
public class FindSingleMessageNode extends BaseNode {

    public FindSingleMessageNode() {
        this.setFlowNodeType(NodeTypeEnum.FIND_SINGLE_MESSAGE.getValue());
        this.setName("获取单条信息任务");
    }
}
