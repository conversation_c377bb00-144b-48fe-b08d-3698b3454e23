package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 插件任务节点
 * 对应NodeTypeEnum.PLUGIN (32)
 */
@Getter
@Setter
@DataBean
public class PluginNode extends BaseNode {

    public PluginNode() {
        this.setTypeId(NodeTypeEnum.PLUGIN.getValue());
        this.setName("插件任务");
    }
}
