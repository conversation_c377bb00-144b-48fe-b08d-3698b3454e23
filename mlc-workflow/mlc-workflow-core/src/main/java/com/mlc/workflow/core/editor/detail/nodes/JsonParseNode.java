package com.mlc.workflow.core.editor.detail.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * JSON 解析任务节点
 * 对应NodeTypeEnum.JSON_PARSE (21)
 */
@Getter
@Setter
public class JsonParseNode extends BaseNode {

    public JsonParseNode() {
        this.setFlowNodeType(NodeTypeEnum.JSON_PARSE.getValue());
        this.setName("JSON 解析任务");
    }
}
