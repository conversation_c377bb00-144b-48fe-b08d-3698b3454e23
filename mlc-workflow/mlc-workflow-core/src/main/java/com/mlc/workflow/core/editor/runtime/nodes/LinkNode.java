package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取链接节点
 * 对应NodeTypeEnum.LINK (15)
 */
@Getter
@Setter
@DataBean
public class LinkNode extends BaseNode {

    public LinkNode() {
        this.setTypeId(NodeTypeEnum.LINK.getValue());
        this.setName("获取链接");
    }
}
