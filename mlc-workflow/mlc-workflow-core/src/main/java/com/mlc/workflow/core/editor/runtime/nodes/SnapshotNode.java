package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取页面快照任务节点
 * 对应NodeTypeEnum.SNAPSHOT (28)
 */
@Getter
@Setter
@DataBean
public class SnapshotNode extends BaseNode {

    public SnapshotNode() {
        this.setTypeId(NodeTypeEnum.SNAPSHOT.getValue());
        this.setName("获取页面快照任务");
    }
}
