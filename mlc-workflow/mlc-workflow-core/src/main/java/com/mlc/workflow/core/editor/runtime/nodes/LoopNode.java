package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 循环任务节点
 * 对应NodeTypeEnum.LOOP (29)
 */
@Getter
@Setter
@DataBean
public class LoopNode extends BaseNode {

    public LoopNode() {
        this.setTypeId(NodeTypeEnum.LOOP.getValue());
        this.setName("循环任务");
    }
}
