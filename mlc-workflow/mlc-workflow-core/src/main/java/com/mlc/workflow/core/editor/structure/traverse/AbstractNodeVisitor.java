package com.mlc.workflow.core.editor.structure.traverse;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;

/**
 * 抽象节点访问者
 * 提供默认的空实现，子类可以选择性重写需要的方法
 */
public abstract class AbstractNodeVisitor implements NodeVisitor {
    
    @Override
    public void visitStartEvent(StartEventNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitGateway(GatewayNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitCondition(ConditionNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitApproval(ApprovalNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitApprovalProcess(ApprovalProcessNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitWrite(WriteNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitCc(CcNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitNotify(NotifyNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitGetMoreRecord(GetMoreRecordNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitSubProcess(SubProcessNode node, TraverseContext context) {
        visitFlowNode(node, context);
    }
    
    @Override
    public void visitSystem(SystemNode node, TraverseContext context) {
        // SystemNode 的默认处理
        // 可以在子类中重写以提供特定的处理逻辑
    }
    
    @Override
    public void visitFlowNode(FlowNode node, TraverseContext context) {
        // 默认空实现
    }
    
    @Override
    public void startVisit(ProcessNode processNode, TraverseContext context) {
        // 默认空实现
    }
    
    @Override
    public void endVisit(ProcessNode processNode, TraverseContext context) {
        // 默认空实现
    }
}
