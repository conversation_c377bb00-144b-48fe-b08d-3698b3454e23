package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.AUTHENTICATION (22)
 */
@Getter
@Setter
@DataBean
public class AuthenticationNode extends BaseNode {

    public AuthenticationNode() {
        this.setTypeId(NodeTypeEnum.AUTHENTICATION.getValue());
        this.setName("API 连接与认证任务");
    }
}
