package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.structure.command.BranchOperations;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.command.NodeOperations;
import com.mlc.workflow.core.editor.structure.transaction.NodeBatchExecutor;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * 工作流编辑器
 * 对外服务接口，整合所有操作命令和校验器，提供统一的编辑入口
 */
@Slf4j
public class WorkflowEditor {
    
    private final GatewayOperations gatewayOperations;
    private final BranchOperations branchOperations;
    private final NodeOperations nodeOperations;
    private final NodeBatchExecutor nodeBatchExecutor;
    private final WorkflowValidator validator;

    public WorkflowEditor(GatewayOperations gatewayOperations, BranchOperations branchOperations,
                          NodeOperations nodeOperations, NodeBatchExecutor nodeBatchExecutor,
                          WorkflowValidator validator) {
        this.gatewayOperations = gatewayOperations;
        this.branchOperations = branchOperations;
        this.nodeOperations = nodeOperations;
        this.nodeBatchExecutor = nodeBatchExecutor;
        this.validator = validator;
    }
    
    // ==================== 网关操作 ====================
    
    /**
     * 新增网关
     * @param processNode 流程节点
     * @param atNodeId 插入点节点ID
     * @param placement 放置策略
     * @return 创建的网关节点
     */
    public GatewayNode addGateway(ProcessNode processNode, String atNodeId,
                                GatewayOperations.PlacementStrategy placement) {
        nodeBatchExecutor.begin(processNode);
        try {
            GatewayNode gateway = gatewayOperations.addGateway(atNodeId, placement);

            if (nodeBatchExecutor.commit()) {
                log.info("成功新增网关: {}", gateway.getId());
                return gateway;
            } else {
                throw new RuntimeException("新增网关失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 删除网关
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     */
    public void deleteGateway(ProcessNode processNode, String gatewayId) {
        nodeBatchExecutor.begin(processNode);
        try {
            gatewayOperations.deleteGateway(gatewayId);

            if (nodeBatchExecutor.commit()) {
                log.info("成功删除网关: {}", gatewayId);
            } else {
                throw new RuntimeException("删除网关失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 切换网关类型
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param toType 目标类型
     */
    public void switchGatewayType(ProcessNode processNode, String gatewayId, Integer toType) {
        nodeBatchExecutor.begin(processNode);
        try {
            gatewayOperations.switchGatewayType(gatewayId, toType);
            
            if (nodeBatchExecutor.commit()) {
                log.info("成功切换网关类型: {} -> {}", gatewayId, toType);
            } else {
                throw new RuntimeException("切换网关类型失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 分支操作 ====================
    
    /**
     * 新增分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param position 插入位置
     * @return 创建的分支叶子节点
     */
    public ConditionNode addBranch(ProcessNode processNode, String gatewayId, int position) {
        nodeBatchExecutor.begin(processNode);
        try {
            ConditionNode branch = branchOperations.addBranch(gatewayId, position);

            if (nodeBatchExecutor.commit()) {
                log.info("成功新增分支: {}", branch.getId());
                return branch;
            } else {
                throw new RuntimeException("新增分支失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 删除分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 分支叶子ID
     */
    public void deleteBranch(ProcessNode processNode, String gatewayId, String branchLeafId) {
        nodeBatchExecutor.begin(processNode);
        try {
            branchOperations.deleteBranch(gatewayId, branchLeafId);

            if (nodeBatchExecutor.commit()) {
                log.info("成功删除分支: {}", branchLeafId);
            } else {
                throw new RuntimeException("删除分支失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }
    
    /**
     * 调整分支顺序
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param newOrder 新的分支顺序
     */
    public void reorderBranches(ProcessNode processNode, String gatewayId, List<String> newOrder) {
        nodeBatchExecutor.begin(processNode);
        try {
            branchOperations.reorderBranches(gatewayId, newOrder);
            
            if (nodeBatchExecutor.commit()) {
                log.info("成功调整分支顺序: {}", gatewayId);
            } else {
                throw new RuntimeException("调整分支顺序失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 复制分支
     * @param processNode 流程节点
     * @param gatewayId 网关ID
     * @param branchLeafId 要复制的分支叶子ID
     * @param position 插入位置
     * @return 复制的分支叶子节点
     */
    public ConditionNode duplicateBranch(ProcessNode processNode, String gatewayId,
                                       String branchLeafId, int position) {
        nodeBatchExecutor.begin(processNode);
        try {
            ConditionNode newBranch = branchOperations.duplicateBranch(gatewayId, branchLeafId, position);

            if (nodeBatchExecutor.commit()) {
                log.info("成功复制分支: {} -> {}", branchLeafId, newBranch.getId());
                return newBranch;
            } else {
                throw new RuntimeException("复制分支失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 普通节点操作 ====================
    
    /**
     * 插入节点
     * @param processNode 流程节点
     * @param afterNodeId 在此节点之后插入
     * @param insertNode 新节点规格
     * @return 创建的节点
     */
    public BaseNode insertNode(ProcessNode processNode, String afterNodeId, BaseNode insertNode) {
        nodeBatchExecutor.begin(processNode);
        try {
            BaseNode newNode = nodeOperations.insertNode(afterNodeId, insertNode);

            if (nodeBatchExecutor.commit()) {
                log.info("成功插入节点: {}", newNode.getId());
                return newNode;
            } else {
                throw new RuntimeException("插入节点失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 删除节点
     * @param processNode 流程节点
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(ProcessNode processNode, String nodeId) {
        nodeBatchExecutor.begin(processNode);
        try {
            nodeOperations.deleteNode(nodeId);

            if (nodeBatchExecutor.commit()) {
                log.info("成功删除节点: {}", nodeId);
            } else {
                throw new RuntimeException("删除节点失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    /**
     * 更新节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(ProcessNode processNode, String nodeId, Map<String, Object> updates) {
        nodeBatchExecutor.begin(processNode);
        try {
            nodeOperations.updateNode(nodeId, updates);

            if (nodeBatchExecutor.commit()) {
                log.info("成功更新节点: {}", nodeId);
            } else {
                throw new RuntimeException("更新节点失败");
            }
        } catch (Exception e) {
            try {
                nodeBatchExecutor.rollback();
            } catch (Exception rollbackException) {
                log.warn("回滚失败: {}", rollbackException.getMessage());
            }
            throw e;
        }
    }

    // ==================== 验证操作 ====================
    
    /**
     * 验证流程
     * @param processNode 流程节点
     * @return 验证结果
     */
    public WorkflowValidator.ValidationResult validate(ProcessNode processNode) {
        return validator.validate(processNode);
    }
    
    // ==================== 查询操作 ====================
    
    /**
     * 查找前驱节点
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 前驱节点列表
     */
    public List<BaseNode> findPrevNodes(ProcessNode processNode, String nodeId) {
        return WorkflowQueryService.findPrevNodes(processNode, nodeId);
    }
    
    /**
     * 查找网关
     * @param processNode 流程节点
     * @param nodeId 节点ID
     * @return 网关节点
     */
    public GatewayNode findGateway(ProcessNode processNode, String nodeId) {
        return WorkflowQueryService.findGateway(processNode, nodeId);
    }
    
    /**
     * 查找分支链
     * @param processNode 流程节点
     * @param branchLeafId 分支叶子ID
     * @return 分支链中的所有节点
     */
    public List<BaseNode> findBranchChain(ProcessNode processNode, String branchLeafId) {
        return WorkflowQueryService.findBranchChain(processNode, branchLeafId);
    }
    
    // ==================== 辅助方法 ====================
    

}
