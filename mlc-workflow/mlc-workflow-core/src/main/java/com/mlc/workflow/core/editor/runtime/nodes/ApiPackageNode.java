package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.API_PACKAGE (24)
 */
@Getter
@Setter
@DataBean
public class ApiPackageNode extends BaseNode {

    public ApiPackageNode() {
        this.setTypeId(NodeTypeEnum.API_PACKAGE.getValue());
        this.setName("API 连接与认证任务");
    }
}
