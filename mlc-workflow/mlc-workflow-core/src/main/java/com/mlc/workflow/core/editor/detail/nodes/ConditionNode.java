package com.mlc.workflow.core.editor.detail.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 分支项任务节点
 * 对应NodeTypeEnum.BRANCH_ITEM (2)
 */
@Getter
@Setter
public class ConditionNode extends BaseNode {

    public ConditionNode() {
        this.setFlowNodeType(NodeTypeEnum.BRANCH_ITEM.getValue());
        this.setName("分支项任务");
    }

    /**
     * 操作条件（表达式）
     */
    private String operateCondition;
}
