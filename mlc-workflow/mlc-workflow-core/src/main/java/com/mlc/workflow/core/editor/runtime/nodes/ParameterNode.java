package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 连接参数任务节点
 * 对应NodeTypeEnum.PARAMETER (23)
 */
@Getter
@Setter
@DataBean
public class ParameterNode extends BaseNode {

    public ParameterNode() {
        this.setTypeId(NodeTypeEnum.PARAMETER.getValue());
        this.setName("连接参数任务");
    }
}
