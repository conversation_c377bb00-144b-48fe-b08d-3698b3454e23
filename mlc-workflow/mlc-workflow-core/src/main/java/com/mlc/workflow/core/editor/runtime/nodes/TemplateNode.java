package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 服务号消息任务节点
 * 对应NodeTypeEnum.TEMPLATE (19)
 */
@Getter
@Setter
@DataBean
public class TemplateNode extends BaseNode {

    public TemplateNode() {
        this.setTypeId(NodeTypeEnum.TEMPLATE.getValue());
        this.setName("服务号消息任务");
    }
}
