package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * AIGC 任务节点
 * 对应NodeTypeEnum.AIGC (31)
 */
@Getter
@Setter
@DataBean
public class AigcNode extends BaseNode {

    public AigcNode() {
        this.setTypeId(NodeTypeEnum.AIGC.getValue());
        this.setName("AIGC 任务");
    }
}
