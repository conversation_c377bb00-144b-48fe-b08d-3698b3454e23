package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 强制中止任务节点
 * 对应NodeTypeEnum.RETURN (30)
 */
@Getter
@Setter
@DataBean
public class ReturnNode extends BaseNode {

    public ReturnNode() {
        this.setTypeId(NodeTypeEnum.RETURN.getValue());
        this.setName("强制中止任务");
    }
}
