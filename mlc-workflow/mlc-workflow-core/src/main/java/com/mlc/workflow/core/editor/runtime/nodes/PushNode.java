package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 界面推送任务节点
 * 对应NodeTypeEnum.PUSH (17)
 */
@Getter
@Setter
@DataBean
public class PushNode extends BaseNode {

    public PushNode() {
        this.setTypeId(NodeTypeEnum.PUSH.getValue());
        this.setName("界面推送任务");
    }
}
