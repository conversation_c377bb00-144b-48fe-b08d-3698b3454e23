package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 邮件任务节点
 * 对应NodeTypeEnum.EMAIL (11)
 */
@Getter
@Setter
@DataBean
public class EmailNode extends BaseNode {

    public EmailNode() {
        this.setTypeId(NodeTypeEnum.EMAIL.getValue());
        this.setName("邮件任务");
    }
}
