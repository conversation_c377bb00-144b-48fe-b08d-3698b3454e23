package com.mlc.workflow.core.editor.detail.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * API 连接与认证任务节点
 * 对应NodeTypeEnum.API_PACKAGE (24)
 */
@Getter
@Setter
public class ApiPackageNode extends BaseNode {

    public ApiPackageNode() {
        this.setFlowNodeType(NodeTypeEnum.API_PACKAGE.getValue());
        this.setName("API 连接与认证任务");
    }
}
