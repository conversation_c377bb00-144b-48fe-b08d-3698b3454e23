package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 发送 API 请求任务节点
 * 对应NodeTypeEnum.WEBHOOK (8)
 */
@Getter
@Setter
@DataBean
public class WebhookNode extends BaseNode {

    public WebhookNode() {
        this.setTypeId(NodeTypeEnum.WEBHOOK.getValue());
        this.setName("发送 API 请求任务");
    }
}
