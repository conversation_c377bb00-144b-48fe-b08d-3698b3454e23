package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取记录打印文件节点
 * 对应NodeTypeEnum.FILE (18)
 */
@Getter
@Setter
@DataBean
public class FileNode extends BaseNode {

    public FileNode() {
        this.setTypeId(NodeTypeEnum.FILE.getValue());
        this.setName("获取记录打印文件");
    }
}
