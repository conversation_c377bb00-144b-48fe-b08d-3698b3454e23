package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * JSON 解析任务节点
 * 对应NodeTypeEnum.JSON_PARSE (21)
 */
@Getter
@Setter
@DataBean
public class JsonParseNode extends BaseNode {

    public JsonParseNode() {
        this.setTypeId(NodeTypeEnum.JSON_PARSE.getValue());
        this.setName("JSON 解析任务");
    }
}
