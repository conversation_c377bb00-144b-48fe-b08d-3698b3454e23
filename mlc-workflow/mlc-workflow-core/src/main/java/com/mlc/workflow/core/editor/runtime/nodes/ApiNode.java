package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 调用已集成 API 任务节点
 * 对应NodeTypeEnum.API (25)
 */
@Getter
@Setter
@DataBean
public class ApiNode extends BaseNode {

    public ApiNode() {
        this.setTypeId(NodeTypeEnum.API.getValue());
        this.setName("调用已集成 API 任务");
    }
}
