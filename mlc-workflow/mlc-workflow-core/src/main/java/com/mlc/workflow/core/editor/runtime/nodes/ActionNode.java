package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 动作任务节点
 * 对应NodeTypeEnum.ACTION (6)
 */
@Getter
@Setter
@DataBean
public class ActionNode extends BaseNode {

    public ActionNode() {
        this.setTypeId(NodeTypeEnum.ACTION.getValue());
        this.setName("动作任务");
    }
}
