package com.mlc.workflow.core.editor.structure.transaction;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * 节点批量执行器
 * 使用暂存区模式管理变更，保证原子性与一致性
 * 所有变更都在工作副本上进行，只有在commit时才应用到原始ProcessNode
 */
@Slf4j
public class NodeBatchExecutor {

    private final WorkflowValidator validator;

    private final ThreadLocal<NodeUnitContext> contextHolder = new ThreadLocal<>();

    public NodeBatchExecutor(WorkflowValidator validator) {
        this.validator = validator;
    }

    /**
     * 开始节点批量执行器
     * @param processNode 流程节点
     */
    public void begin(ProcessNode processNode) {
        if (contextHolder.get() != null) {
            throw new IllegalStateException("已经开始执行，请先提交或回滚");
        }

        NodeUnitContext context = new NodeUnitContext(processNode);
        contextHolder.set(context);

        log.debug("开始执行，流程: {}", processNode.getId());
    }

    /**
     * 获取工作副本ProcessNode
     * 所有操作都应该在工作副本上进行
     * @return 工作副本ProcessNode
     */
    public ProcessNode getWorkingCopy() {
        NodeUnitContext context = getCurrentContext();
        return context.getWorkingCopy();
    }

    /**
     * 在工作副本中创建节点
     * @param node 新创建的节点
     */
    public void createNode(BaseNode node) {
        NodeUnitContext context = getCurrentContext();
        context.getStagingArea().createNode(node);
        log.debug("在工作副本中创建节点: {}", node.getId());
    }

    /**
     * 在工作副本中更新节点
     * @param nodeId 节点ID
     * @param updatedNode 更新后的节点
     */
    public void updateNode(String nodeId, BaseNode updatedNode) {
        NodeUnitContext context = getCurrentContext();
        context.getStagingArea().updateNode(nodeId, updatedNode);
        log.debug("在工作副本中更新节点: {}", nodeId);
    }

    /**
     * 在工作副本中删除节点
     * @param nodeId 节点ID
     */
    public void deleteNode(String nodeId) {
        NodeUnitContext context = getCurrentContext();
        context.getStagingArea().deleteNode(nodeId);
        log.debug("在工作副本中删除节点: {}", nodeId);
    }

    /**
     * 获取工作副本中的节点
     * @param nodeId 节点ID
     * @return 节点实例
     */
    public BaseNode getNode(String nodeId) {
        NodeUnitContext context = getCurrentContext();
        return context.getStagingArea().getNode(nodeId);
    }
    
    /**
     * 提交节点批量执行器
     * @return 是否提交成功
     */
    public boolean commit() {
        NodeUnitContext context = getCurrentContext();

        if (context.committed || context.rolledBack) {
            throw new IllegalStateException("节点批量执行器已经提交或回滚");
        }

        try {
            // 提交前验证工作副本
            WorkflowValidator.ValidationResult validationResult = validator.validate(context.getWorkingCopy());
            if (!validationResult.isValid()) {
                log.error("节点批量执行器提交前验证失败: {}", validationResult.getErrors());
                rollback();
                return false;
            }

            // 应用所有变更到原始ProcessNode
            context.getStagingArea().applyChangesToOriginal();

            context.committed = true;

            StagingArea.ChangeStatistics stats = context.getStagingArea().getChangeStatistics();
            log.debug("节点批量执行器提交成功，{}", stats);
            return true;
        } catch (Exception e) {
            log.error("节点批量执行器提交失败", e);
            rollback();
            return false;
        } finally {
            contextHolder.remove();
        }
    }

    /**
     * 回滚节点批量执行器
     */
    public void rollback() {
        NodeUnitContext context = getCurrentContext();

        if (context.committed) {
            throw new IllegalStateException("节点批量执行器已经提交，无法回滚");
        }

        if (context.rolledBack) {
            return; // 已经回滚过了
        }

        try {
            // 清理暂存区（丢弃所有变更）
            context.getStagingArea().clear();

            context.rolledBack = true;

            log.debug("节点批量执行器回滚成功，所有变更已丢弃");

        } catch (Exception e) {
            log.error("节点批量执行器回滚失败", e);
        } finally {
            contextHolder.remove();
        }
    }
    
    /**
     * 获取当前上下文
     */
    private NodeUnitContext getCurrentContext() {
        NodeUnitContext context = contextHolder.get();
        if (context == null) {
            throw new IllegalStateException("节点批量执行器未开始");
        }
        return context;
    }
    
    /**
     * 检查是否在节点批量执行器中
     * @return 是否在节点批量执行器中
     */
    public boolean isInTransaction() {
        return contextHolder.get() != null;
    }

    /**
     * 检查是否有变更
     * @return 是否有变更
     */
    public boolean hasChanges() {
        NodeUnitContext context = contextHolder.get();
        return context != null && context.getStagingArea().hasChanges();
    }

    /**
     * 获取变更统计信息
     * @return 变更统计信息
     */
    public StagingArea.ChangeStatistics getChangeStatistics() {
        NodeUnitContext context = contextHolder.get();
        return context != null ? context.getStagingArea().getChangeStatistics() :
               new StagingArea.ChangeStatistics(0, 0, 0);
    }


    /**
     * 节点批量执行器上下文
     */
    @Getter
    private static class NodeUnitContext {
        private final ProcessNode originalProcessNode;
        private final StagingArea stagingArea;
        private boolean committed;
        private boolean rolledBack;

        public NodeUnitContext(ProcessNode processNode) {
            this.originalProcessNode = processNode;
            this.stagingArea = new StagingArea(processNode);
            this.committed = false;
            this.rolledBack = false;
        }

        public ProcessNode getWorkingCopy() {
            return stagingArea.getWorkingCopy();
        }
    }
}
