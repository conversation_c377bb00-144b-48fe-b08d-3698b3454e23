package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 获取多条信息任务节点
 * 对应NodeTypeEnum.FIND_MORE_MESSAGE (1001)
 */
@Getter
@Setter
@DataBean
public class FindMoreMessageNode extends BaseNode {

    public FindMoreMessageNode() {
        this.setTypeId(NodeTypeEnum.FIND_MORE_MESSAGE.getValue());
        this.setName("获取多条信息任务");
    }
}
