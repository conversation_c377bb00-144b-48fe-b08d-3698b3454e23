package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 延时任务节点
 * 对应NodeTypeEnum.DELAY (12)
 */
@Getter
@Setter
@DataBean
public class DelayNode extends BaseNode {

    public DelayNode() {
        this.setTypeId(NodeTypeEnum.DELAY.getValue());
        this.setName("延时任务");
    }
}
