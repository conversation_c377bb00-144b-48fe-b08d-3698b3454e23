package com.mlc.workflow.core.editor.runtime.nodes;

import com.mlc.base.common.enums.workflow.NodeTypeEnum;
import io.nop.api.core.annotations.data.DataBean;
import lombok.Getter;
import lombok.Setter;

/**
 * 代码块任务节点
 * 对应NodeTypeEnum.CODE (14)
 */
@Getter
@Setter
@DataBean
public class CodeNode extends BaseNode {

    public CodeNode() {
        this.setTypeId(NodeTypeEnum.CODE.getValue());
        this.setName("代码块任务");
    }
}
