package com.mlc.workflow.core.editor.structure.command;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.BaseNode;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.autowire.AutoWireStrategy;
import com.mlc.workflow.core.editor.structure.utils.WorkflowQueryService;
import com.mlc.workflow.core.editor.structure.transaction.NodeBatchExecutor;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

/**
 * 普通节点操作命令
 * 实现普通节点的新增、删除、修改等操作
 */
@Slf4j
public class NodeOperations {
    
    private final NodeBatchExecutor nodeBatchExecutor;
    private final AutoWireStrategy autoWireStrategy;
    
    public NodeOperations(NodeBatchExecutor nodeBatchExecutor, AutoWireStrategy autoWireStrategy) {
        this.nodeBatchExecutor = nodeBatchExecutor;
        this.autoWireStrategy = autoWireStrategy;
    }
    

    /**
     * 插入节点
     * @param afterNodeId 在此节点之后插入
     * @param newNode 新节点规格
     * @return 创建的节点
     */
    public BaseNode insertNode(String afterNodeId, BaseNode newNode) {
        if (nodeBatchExecutor == null || afterNodeId == null || newNode == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNode afterNode = workingCopy.getFlowNodeMap().get(afterNodeId);
        if (afterNode == null) {
            throw new IllegalArgumentException("找不到插入点节点: " + afterNodeId);
        }

        if (!(afterNode instanceof IRoutable routableAfterNode)) {
            throw new IllegalArgumentException("插入点节点必须是可路由的");
        }

        String originalNextId = routableAfterNode.getNextId();

        nodeBatchExecutor.createNode(newNode);

        // 使用AutoWireStrategy进行连接
        List<BaseNode> prevNodes = Arrays.asList(afterNode);

        if ("99".equals(originalNextId)) {
            // 原来指向结束，使用ConnectToEnd
            autoWireStrategy.spliceBetween(workingCopy, prevNodes, newNode, newNode, null);
            autoWireStrategy.connectToEnd(workingCopy, newNode);
        } else {
            // 普通插入
            autoWireStrategy.spliceBetween(workingCopy, prevNodes, newNode, newNode, originalNextId);

            // 更新下一个节点的前驱
            if (originalNextId != null && !originalNextId.trim().isEmpty()) {
                BaseNode nextNode = workingCopy.getFlowNodeMap().get(originalNextId);
                if (nextNode instanceof IRoutable routableNext) {
                    routableNext.setPrveId(newNode.getId());
                    nodeBatchExecutor.updateNode(originalNextId, nextNode);
                }
            }
        }

        // 设置新节点的路由信息
        if (newNode instanceof IRoutable routableNew) {
            routableNew.setPrveId(afterNodeId);
        }

        // 更新afterNode
        nodeBatchExecutor.updateNode(afterNodeId, afterNode);

        log.debug("在节点 {} 后插入新节点 {}", afterNodeId, newNode.getId());

        return newNode;
    }
    
    /**
     * 删除节点
     * @param nodeId 要删除的节点ID
     */
    public void deleteNode(String nodeId) {
        if (nodeBatchExecutor == null || nodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNode nodeToDelete = workingCopy.getFlowNodeMap().get(nodeId);
        if (nodeToDelete == null) {
            throw new IllegalArgumentException("找不到要删除的节点: " + nodeId);
        }

        if (!(nodeToDelete instanceof IRoutable routableNode)) {
            throw new IllegalArgumentException("只能删除可路由的节点");
        }

        // 查找前驱节点
        List<BaseNode> prevNodes = WorkflowQueryService.findPrevNodes(workingCopy, nodeId);
        String nextId = routableNode.getNextId();

        // 连接前驱节点到后继节点
        for (BaseNode prevNode : prevNodes) {
            if (prevNode instanceof IRoutable routablePrev) {
                routablePrev.setNextId(nextId);
                nodeBatchExecutor.updateNode(prevNode.getId(), prevNode);
            }
        }

        // 更新后继节点的前驱
        if (nextId != null && !nextId.trim().isEmpty() && !"99".equals(nextId)) {
            BaseNode nextNode = workingCopy.getFlowNodeMap().get(nextId);
            if (nextNode instanceof IRoutable routableNext && !prevNodes.isEmpty()) {
                // 如果有多个前驱，选择第一个作为新的前驱
                routableNext.setPrveId(prevNodes.get(0).getId());
                nodeBatchExecutor.updateNode(nextId, nextNode);
            }
        }

        // 如果删除的是EndOwner，需要重新选择EndOwner
        if ("99".equals(nextId)) {
            handleEndOwnerDeletion(workingCopy, prevNodes);
        }

        nodeBatchExecutor.deleteNode(nodeId);

        log.debug("删除节点 {}", nodeId);
    }
    
    /**
     * 更新节点
     * @param nodeId 节点ID
     * @param updates 更新内容
     */
    public void updateNode(String nodeId, Map<String, Object> updates) {
        if (nodeBatchExecutor == null || nodeId == null || updates == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        ProcessNode workingCopy = nodeBatchExecutor.getWorkingCopy();
        BaseNode node = workingCopy.getFlowNodeMap().get(nodeId);
        if (node == null) {
            throw new IllegalArgumentException("找不到节点: " + nodeId);
        }

        // 应用更新
        applyUpdatesToNode(node, updates);

        nodeBatchExecutor.updateNode(nodeId, node);

        // 检查是否需要连接到结束
        Boolean connectToEnd = (Boolean) updates.get("connectToEnd");
        if (Boolean.TRUE.equals(connectToEnd) && node instanceof IRoutable) {
            autoWireStrategy.connectToEnd(workingCopy, node);
        }

        log.debug("更新节点 {}", nodeId);
    }

    /**
     * 处理EndOwner删除
     */
    private void handleEndOwnerDeletion(ProcessNode processNode, List<BaseNode> prevNodes) {
        if (prevNodes.isEmpty()) {
            log.warn("删除EndOwner后没有前驱节点");
            return;
        }
        
        // 选择最后一个前驱作为新的EndOwner
        BaseNode newEndOwner = prevNodes.get(prevNodes.size() - 1);
        if (newEndOwner instanceof IRoutable routableNewEnd) {
            routableNewEnd.setNextId("99");
            
            // 其他前驱指向新的EndOwner
            for (int i = 0; i < prevNodes.size() - 1; i++) {
                BaseNode prevNode = prevNodes.get(i);
                if (prevNode instanceof IRoutable routablePrev) {
                    routablePrev.setNextId(newEndOwner.getId());
                }
            }
            
            log.debug("重新选择EndOwner: {}", newEndOwner.getId());
        }
    }
    
    /**
     * 应用更新到节点
     */
    private void applyUpdatesToNode(BaseNode node, Map<String, Object> updates) {
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            Object value = entry.getValue();
            node.setName((String) value);
        }
    }
    
    /**
     * 移动节点到新位置
     * @param processNode 流程节点
     * @param nodeId 要移动的节点ID
     * @param newAfterNodeId 新的前驱节点ID
     * @deprecated 此方法尚未重构为使用nodeBatchExecutor
     */
    @Deprecated
    public void moveNode(ProcessNode processNode, String nodeId, String newAfterNodeId) {
        if (processNode == null || nodeId == null || newAfterNodeId == null) {
            throw new IllegalArgumentException("参数不能为空");
        }
        
        BaseNode nodeToMove = processNode.getFlowNodeMap().get(nodeId);
        if (nodeToMove == null) {
            throw new IllegalArgumentException("找不到要移动的节点: " + nodeId);
        }
        
        // 先断开原有连接
        autoWireStrategy.detach(processNode, nodeToMove, nodeToMove);
        
        // 连接到新位置
        BaseNode newAfterNode = processNode.getFlowNodeMap().get(newAfterNodeId);
        if (newAfterNode instanceof IRoutable routableAfter) {
            String originalNext = routableAfter.getNextId();
            autoWireStrategy.spliceBetween(processNode, Arrays.asList(newAfterNode),
                                         nodeToMove, nodeToMove, originalNext);
        }
        
        log.debug("移动节点 {} 到节点 {} 之后", nodeId, newAfterNodeId);
    }
}
