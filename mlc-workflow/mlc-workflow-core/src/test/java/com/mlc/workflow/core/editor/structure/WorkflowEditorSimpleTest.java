package com.mlc.workflow.core.editor.structure;

import com.mlc.workflow.core.editor.runtime.beans.ProcessNode;
import com.mlc.workflow.core.editor.runtime.nodes.*;
import com.mlc.workflow.core.editor.runtime.nodes.capability.IRoutable;
import com.mlc.workflow.core.editor.structure.command.GatewayOperations;
import com.mlc.workflow.core.editor.structure.utils.WorkflowValidator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 工作流编辑器简化集成测试
 * 验证核心编辑功能而不依赖复杂的Spring配置
 */
public class WorkflowEditorSimpleTest {
    
    private WorkflowEditor workflowEditor;
    private ProcessNode testProcessNode;
    
    @BeforeEach
    void setUp() {
        workflowEditor = new WorkflowEditor(null, null, null, null, null);
        testProcessNode = createBasicWorkflow();
    }
    

    /**
     * 创建基础工作流：开始 -> 审批 -> 结束
     */
    private ProcessNode createBasicWorkflow() {
        ProcessNode processNode = new ProcessNode();
        processNode.setId("test-process");
        processNode.setFlowNodeMap(new HashMap<>());
        
        // 开始事件
        StartEventNode startEvent = new StartEventNode();
        startEvent.setId("start-1");
        startEvent.setName("开始");
        startEvent.setNextId("approval-1");
        
        // 审批节点
        ApprovalNode approvalNode = new ApprovalNode();
        approvalNode.setId("approval-1");
        approvalNode.setName("审批");
        approvalNode.setPrveId("start-1");
        approvalNode.setNextId("99");
        
        // 添加到流程映射
        processNode.getFlowNodeMap().put(startEvent.getId(), startEvent);
        processNode.getFlowNodeMap().put(approvalNode.getId(), approvalNode);
        processNode.setStartEventId(startEvent.getId());
        
        return processNode;
    }
    
    @Test
    void testValidateBasicWorkflow() {
        // 测试基础工作流验证
        WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
        
        assertTrue(result.isValid(), "基础工作流应该有效: " + result.getErrors());
        assertEquals(0, result.getErrors().size());
    }
    
    @Test
    void testAddGateway() {
        // 测试添加网关
        try {
            GatewayNode gateway = workflowEditor.addGateway(testProcessNode, "start-1", 
                                                          GatewayOperations.PlacementStrategy.NO_MOVE);
            
            assertNotNull(gateway);
            assertNotNull(gateway.getId());
            assertEquals("网关", gateway.getName());
            assertEquals(2, gateway.getFlowIds().size());
            
            // 验证网关已添加到流程中
            assertTrue(testProcessNode.getFlowNodeMap().containsKey(gateway.getId()));
            
            // 验证流程仍然有效
            WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
            assertTrue(result.isValid(), "添加网关后流程应该有效: " + result.getErrors());
            
        } catch (Exception e) {
            fail("添加网关失败: " + e.getMessage());
        }
    }
    
    @Test
    void testInsertNode() {
        // 测试插入节点
        try {
            WriteNode writeNode = new WriteNode();
            writeNode.setId("write-new");
            writeNode.setName("填写表单");
            
            BaseNode insertedNode = workflowEditor.insertNode(testProcessNode, "start-1", writeNode);
            
            assertNotNull(insertedNode);
            assertEquals("write-new", insertedNode.getId());
            
            // 验证节点已插入
            assertTrue(testProcessNode.getFlowNodeMap().containsKey("write-new"));
            
            // 验证连接关系
            BaseNode startNode = testProcessNode.getFlowNodeMap().get("start-1");
            if (startNode instanceof IRoutable) {
                IRoutable routableStart = (IRoutable) startNode;
                assertEquals("write-new", routableStart.getNextId());
            }
            
            if (insertedNode instanceof IRoutable) {
                IRoutable routableInserted = (IRoutable) insertedNode;
                assertEquals("approval-1", routableInserted.getNextId());
            }
            
            // 验证流程仍然有效
            WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
            assertTrue(result.isValid(), "插入节点后流程应该有效: " + result.getErrors());
            
        } catch (Exception e) {
            fail("插入节点失败: " + e.getMessage());
        }
    }
    
    @Test
    void testDeleteNode() {
        // 先添加一个节点，然后删除它
        try {
            // 添加节点
            WriteNode writeNode = new WriteNode();
            writeNode.setId("write-temp");
            writeNode.setName("临时节点");
            
            workflowEditor.insertNode(testProcessNode, "start-1", writeNode);
            
            // 验证节点已添加
            assertTrue(testProcessNode.getFlowNodeMap().containsKey("write-temp"));
            
            // 删除节点
            workflowEditor.deleteNode(testProcessNode, "write-temp");
            
            // 验证节点已删除
            assertFalse(testProcessNode.getFlowNodeMap().containsKey("write-temp"));
            
            // 验证连接已恢复
            BaseNode startNode = testProcessNode.getFlowNodeMap().get("start-1");
            if (startNode instanceof IRoutable) {
                IRoutable routableStart = (IRoutable) startNode;
                assertEquals("approval-1", routableStart.getNextId());
            }
            
            // 验证流程仍然有效
            WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
            assertTrue(result.isValid(), "删除节点后流程应该有效: " + result.getErrors());
            
        } catch (Exception e) {
            fail("删除节点失败: " + e.getMessage());
        }
    }
    
    @Test
    void testComplexWorkflowOperations() {
        // 测试复杂的工作流操作序列
        try {
            // 1. 添加网关
            GatewayNode gateway = workflowEditor.addGateway(testProcessNode, "start-1", 
                                                          GatewayOperations.PlacementStrategy.NO_MOVE);
            
            // 2. 在网关分支中添加节点
            String branchId = gateway.getFlowIds().get(0);
            WriteNode writeNode = new WriteNode();
            writeNode.setId("write-in-branch");
            writeNode.setName("分支中的填写");
            
            workflowEditor.insertNode(testProcessNode, branchId, writeNode);
            
            // 3. 验证结构
            assertTrue(testProcessNode.getFlowNodeMap().containsKey(gateway.getId()));
            assertTrue(testProcessNode.getFlowNodeMap().containsKey("write-in-branch"));
            
            // 4. 验证流程有效性
            WorkflowValidator.ValidationResult result = workflowEditor.validate(testProcessNode);
            assertTrue(result.isValid(), "复杂操作后流程应该有效: " + result.getErrors());
            
            // 5. 验证EndOwner唯一性
            long endOwnerCount = testProcessNode.getFlowNodeMap().values().stream()
                    .filter(node -> node instanceof IRoutable)
                    .map(node -> (IRoutable) node)
                    .filter(routable -> "99".equals(routable.getNextId()))
                    .count();
            
            assertEquals(1, endOwnerCount, "应该只有一个EndOwner");
            
        } catch (Exception e) {
            fail("复杂工作流操作失败: " + e.getMessage());
        }
    }
}
